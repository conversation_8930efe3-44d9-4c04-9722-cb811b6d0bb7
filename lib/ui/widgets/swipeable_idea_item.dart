import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:voji/models/models.dart';
import 'package:voji/services/audio/audio_providers.dart';
import 'package:voji/services/limits/app_limits_providers.dart';
import 'package:voji/services/llm/llm_providers.dart';
import 'package:voji/services/llm/llm_service.dart';
import 'package:voji/ui/providers/firestore_idea_provider.dart';
import 'package:voji/ui/providers/idea_edit_provider.dart';
import 'package:voji/ui/providers/idea_edit_recording_provider.dart';
import 'package:voji/ui/providers/idea_edit_transcription_provider.dart';
import 'package:voji/ui/theme/voji_theme.dart';
import 'package:voji/ui/widgets/advanced_audio_recording_panel.dart';
import 'package:voji/ui/widgets/common/swipeable_item.dart';
import 'package:voji/utils/logger.dart';

/// Provider to track which idea is currently in swipe mode
final swipedIdeaIdProvider = StateProvider<String?>((ref) => null);

/// Widget for displaying an individual idea in the list with swipe functionality
class SwipeableIdeaItem extends ConsumerStatefulWidget {
  /// The idea to display
  final Idea idea;

  /// The ideabook ID this idea belongs to
  final String ideabookId;

  // Context menu constants
  static const double _maxSwipeExtent =
      60.0; // Maximum swipe distance for context menu (1 icon)

  /// Constructor
  const SwipeableIdeaItem({
    super.key,
    required this.idea,
    required this.ideabookId,
  });

  @override
  ConsumerState<SwipeableIdeaItem> createState() => _SwipeableIdeaItemState();
}

class _SwipeableIdeaItemState extends ConsumerState<SwipeableIdeaItem> {
  // Text controller for editing
  late TextEditingController _textEditingController;
  late FocusNode _focusNode;

  // Flag to track if we've appended text
  bool _hasAppendedText = false;
  // Flag to track if we've just appended text and should not reset
  bool _justAppendedText = false;
  // Flag to control whether the text field should be focused
  bool _shouldFocusTextField = true;
  // Store the current text to prevent resets
  String _currentText = "";

  // Cached max idea words limit
  int? _maxIdeaWords;

  @override
  void initState() {
    super.initState();

    // Initialize with the idea content
    _currentText = widget.idea.content;
    Logger.debug(
      'SwipeableIdeaItem initState - Idea ID: ${widget.idea.id}, Content: "${_currentText.substring(0, _currentText.length.clamp(0, 20))}${_currentText.length > 20 ? "..." : ""}"',
    );
    _textEditingController = TextEditingController(text: _currentText);
    _focusNode = FocusNode();

    // Initialize the max idea words limit
    _initializeMaxIdeaWords();

    // Add listener to text controller to debug changes
    _textEditingController.addListener(() {
      final text = _textEditingController.text;
      Logger.debug('TextEditingController updated: ${text.length} chars');

      // Store the current text to prevent resets
      if (text != _currentText) {
        _currentText = text;
        Logger.debug('Updated _currentText to: ${_currentText.length} chars');
      }
    });

    // Add a listener to the transcription provider
    Future.microtask(() {
      ref.listenManual(ideaEditTranscriptionProvider, (previous, next) {
        if (next != null && next.ideaId == widget.idea.id) {
          Logger.debug(
            'Transcription provider updated in listener: ${next.transcribedText}',
          );
          _appendTranscribedText(next.transcribedText);
          // Reset the provider
          ref.read(ideaEditTranscriptionProvider.notifier).state = null;
        }
      });
    });
  }

  /// Initialize the maximum idea words limit from the provider
  Future<void> _initializeMaxIdeaWords() async {
    try {
      _maxIdeaWords = await ref.read(ideaMaxWordsProvider.future);
      Logger.debug('SwipeableIdeaItem: Max idea words initialized: $_maxIdeaWords');
    } catch (e) {
      Logger.error('SwipeableIdeaItem: Failed to get max idea words, using fallback', e);
      _maxIdeaWords = 1000; // Fallback to free tier limit
    }
  }

  @override
  void didUpdateWidget(SwipeableIdeaItem oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the idea content has changed (e.g., from Firestore update)
    if (widget.idea.content != oldWidget.idea.content) {
      Logger.debug('SwipeableIdeaItem didUpdateWidget - Idea content changed');
      Logger.debug(
        'Old content: "${oldWidget.idea.content.substring(0, oldWidget.idea.content.length.clamp(0, 20))}${oldWidget.idea.content.length > 20 ? "..." : ""}"',
      );
      Logger.debug(
        'New content: "${widget.idea.content.substring(0, widget.idea.content.length.clamp(0, 20))}${widget.idea.content.length > 20 ? "..." : ""}"',
      );

      // Don't update if we just appended text
      if (_justAppendedText) {
        Logger.debug(
          'Not updating text controller because we just appended text',
        );
        return;
      }

      // Only update if we're not in edit mode or if the user hasn't made changes
      final isEditing = ref.read(ideaEditProvider) == widget.idea.id;
      final userHasEditedText =
          _textEditingController.text != oldWidget.idea.content;

      if (!isEditing || !userHasEditedText) {
        Logger.debug('Updating _currentText with new idea content');
        _currentText = widget.idea.content;

        // Only update the controller if it's different to avoid cursor position reset
        if (_textEditingController.text != _currentText) {
          _textEditingController.text = _currentText;
        }
      } else {
        Logger.debug('Not updating text controller because user is editing');
      }
    }

    // Check if edit mode has been entered or exited
    final wasEditing = ref.read(ideaEditProvider) == oldWidget.idea.id;
    final isEditing = ref.read(ideaEditProvider) == widget.idea.id;

    if (wasEditing && !isEditing) {
      // We've exited edit mode, reset to original content
      Logger.debug('Exited edit mode, resetting to original content');
      _resetToOriginalContent();
    } else if (!wasEditing && isEditing) {
      // We've entered edit mode, reset to original content
      Logger.debug('Entered edit mode, resetting to original content');
      _resetToOriginalContent();
    }
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  /// Insert transcribed text at the current cursor position
  void _appendTranscribedText(String transcribedText) {
    Logger.debug(
      'Inserting transcribed text at cursor position for idea ${widget.idea.id}',
    );
    Logger.debug('Current text length: ${_textEditingController.text.length}');
    Logger.debug('Transcribed text length: ${transcribedText.length}');

    // Get the current text and cursor position
    final currentText = _textEditingController.text;
    final selection = _textEditingController.selection;

    // Determine where to insert the transcribed text
    String newText;
    int newCursorPosition;

    if (selection.isValid) {
      // Insert at cursor position
      final beforeCursor = currentText.substring(0, selection.start);
      final afterCursor = currentText.substring(selection.end);

      // Create the new text with the transcription inserted at cursor (no space added)
      newText = beforeCursor + transcribedText + afterCursor;

      // Calculate new cursor position (after the inserted text)
      newCursorPosition = beforeCursor.length + transcribedText.length;
    } else {
      // If no valid selection, append to the end (no space added)
      newText =
          currentText.isEmpty ? transcribedText : currentText + transcribedText;
      newCursorPosition = newText.length;
    }

    Logger.debug('New text length: ${newText.length}');

    // Update our stored text first
    _currentText = newText;
    _hasAppendedText = true;
    _justAppendedText = true; // Set flag to prevent resetting in _buildEditRow

    // Update the text controller
    _textEditingController.value = TextEditingValue(
      text: newText,
      selection: TextSelection.fromPosition(
        TextPosition(offset: newCursorPosition),
      ),
    );

    // Force a rebuild
    setState(() {
      // This ensures the UI updates with the new text
      Logger.debug('Forcing UI update after inserting text at cursor position');
    });

    // Re-focus the text field to allow immediate editing
    _focusNode.requestFocus();

    Logger.debug(
      'Inserted transcribed text at cursor position for idea ${widget.idea.id}',
    );
  }

  @override
  Widget build(BuildContext context) {
    // Use the generic SwipeableItem widget
    return SwipeableItem(
      itemId: widget.idea.id,
      swipedItemProvider: swipedIdeaIdProvider,
      editItemProvider: ideaEditProvider,
      maxSwipeExtent: SwipeableIdeaItem._maxSwipeExtent,
      contextMenuBuilder:
          (context, totalMenuWidth) =>
              _buildContextMenu(context, totalMenuWidth),
      contentBuilder: (context) => _buildNormalRow(context),
      editModeBuilder: (context) => _buildEditRow(context),
      showDivider:
          false, // Don't show divider in SwipeableItem to avoid double borders
    );
  }

  /// Build the context menu that appears when swiped
  Widget _buildContextMenu(BuildContext context, double totalMenuWidth) {
    // Get the right padding from the main row
    const double rightPadding = 16.0;
    const double iconWidth = 40.0; // Width of each icon

    return Container(
      width: totalMenuWidth,
      height: double.infinity, // Make sure it stretches to full height
      color: Theme.of(context).scaffoldBackgroundColor,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.end,
        crossAxisAlignment:
            CrossAxisAlignment.center, // Center icons vertically
        children: [
          // Delete icon (only icon in the menu now)
          SizedBox(
            width: iconWidth,
            child: Center(
              child: IconButton(
                icon: Icon(
                  Icons.delete,
                  color: VojiTheme.colorsOf(context).textPrimary,
                  size: 24,
                ),
                onPressed: () {
                  // Show confirmation dialog before deleting
                  _showDeleteConfirmationDialog(context);
                },
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
              ),
            ),
          ),

          // Right padding
          SizedBox(width: rightPadding),
        ],
      ),
    );
  }

  /// Build the normal row display for the idea
  Widget _buildNormalRow(BuildContext context) {
    // Format the creation date
    final formattedDate = DateFormat(
      'MMM d, yyyy',
    ).format(widget.idea.createdAt);

    // Log the current idea content for debugging
    Logger.debug('_buildNormalRow - Idea ID: ${widget.idea.id}');
    Logger.debug(
      '_buildNormalRow - Content: "${widget.idea.content.substring(0, widget.idea.content.length.clamp(0, 20))}${widget.idea.content.length > 20 ? "..." : ""}"',
    );

    // Check if this idea is in swipe mode
    final swipedId = ref.watch(swipedIdeaIdProvider);
    final isSwiped = swipedId == widget.idea.id;

    return InkWell(
      onTap: () {
        if (isSwiped) {
          // If swiped, close the context menu instead of entering edit mode
          ref.read(swipedIdeaIdProvider.notifier).state = null;
        } else {
          // Enter edit mode when tapping anywhere on the row
          _resetToOriginalContent();
          // Reset the focus flag when entering edit mode
          setState(() {
            _shouldFocusTextField = true;
          });
          ref.read(ideaEditProvider.notifier).state = widget.idea.id;
        }
      },
      child: Align(
        alignment: Alignment.centerLeft,
        child: Padding(
          padding: const EdgeInsets.all(12.0), // Reduced padding
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            mainAxisSize: MainAxisSize.min, // Use minimum space needed
            children: [
              // Idea content
              Text(
                widget.idea.content,
                style: VojiTheme.textStylesOf(context).bodyMedium,
                textAlign: TextAlign.left,
              ),

              // Date
              const SizedBox(height: 4), // Reduced spacing
              Text(
                formattedDate,
                style: VojiTheme.textStylesOf(context).bodySmall,
                textAlign: TextAlign.left,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build the edit row display
  Widget _buildEditRow(BuildContext context) {
    // Check if in recording mode
    final isRecording = ref.watch(ideaEditRecordingProvider) == widget.idea.id;
    final isEditing = ref.read(ideaEditProvider) == widget.idea.id;

    Logger.debug(
      '_buildEditRow - Idea ID: ${widget.idea.id}, isEditing: $isEditing',
    );
    Logger.debug(
      '_buildEditRow - Widget idea content: "${widget.idea.content.substring(0, widget.idea.content.length.clamp(0, 20))}${widget.idea.content.length > 20 ? "..." : ""}"',
    );
    Logger.debug(
      '_buildEditRow - Current text: "${_currentText.substring(0, _currentText.length.clamp(0, 20))}${_currentText.length > 20 ? "..." : ""}"',
    );
    Logger.debug(
      '_buildEditRow - Text controller: "${_textEditingController.text.substring(0, _textEditingController.text.length.clamp(0, 20))}${_textEditingController.text.length > 20 ? "..." : ""}"',
    );

    // Check if we just appended text - if so, don't reset the text controller
    if (_justAppendedText) {
      Logger.debug('Just appended text, not updating text controller');
      // Reset the flag for next time
      _justAppendedText = false;
    }
    // When entering edit mode, always ensure we have the original content from the widget
    else if (isEditing && _textEditingController.text != _currentText) {
      // We're in edit mode but the text controller doesn't match our current text
      // This happens when we've just entered edit mode
      Logger.debug('Updating text controller in edit mode');
      _textEditingController.text = _currentText;
      _textEditingController.selection = TextSelection.fromPosition(
        TextPosition(offset: _textEditingController.text.length),
      );
    }

    // Check for transcription result - use read instead of watch to avoid rebuild loops
    final transcription = ref.read(ideaEditTranscriptionProvider);
    if (transcription != null && transcription.ideaId == widget.idea.id) {
      Logger.debug(
        'Found transcription in _buildEditRow: ${transcription.transcribedText}',
      );
      // Schedule the append operation after the build is complete
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _appendTranscribedText(transcription.transcribedText);
        // Reset the provider
        ref.read(ideaEditTranscriptionProvider.notifier).state = null;
      });
    }

    // Request focus when entering edit mode or after recording, but only if _shouldFocusTextField is true
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (isEditing && _shouldFocusTextField) {
        _focusNode.requestFocus();
      }
    });

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Text field for editing the content
        Align(
          alignment: Alignment.centerLeft,
          child: Container(
            // Calculate approximate height for 10 lines of text
            // This is an estimate based on the line height of bodyMedium text style
            constraints: const BoxConstraints(
              maxHeight: 240, // Approximate height for 10 lines (24px per line)
            ),
            child: SingleChildScrollView(
              child: TextField(
                key: ValueKey(
                  'idea_edit_${widget.idea.id}_${_currentText.length}_$_hasAppendedText',
                ),
                controller: _textEditingController,
                focusNode: _focusNode,
                style: VojiTheme.textStylesOf(context).bodyMedium,
                textAlign: TextAlign.left,
                decoration: InputDecoration(
                  border: InputBorder.none,
                  contentPadding: EdgeInsets.zero,
                  isDense: true,
                  alignLabelWithHint: true,
                ),
                maxLines: null, // Allow multiple lines
                textInputAction: TextInputAction.newline,
                // Always keep enabled, even during recording, to preserve cursor position
                enabled: true,
                // Don't save on Enter, just insert a newline
                onEditingComplete: () {
                  // Do nothing, let the newline be inserted
                },
                onChanged: (text) {
                  // Update our stored text when the user edits it directly
                  _currentText = text;
                },
                onTap: () {
                  // Reset the focus flag when user taps on the text field
                  if (!_shouldFocusTextField) {
                    setState(() {
                      _shouldFocusTextField = true;
                    });
                  }
                },
              ),
            ),
          ),
        ),

        // Date (not editable)
        const SizedBox(height: 8),
        Align(
          alignment: Alignment.centerLeft,
          child: Text(
            DateFormat('MMM d, yyyy').format(widget.idea.createdAt),
            style: VojiTheme.textStylesOf(context).bodySmall,
            textAlign: TextAlign.left,
          ),
        ),

        // Buttons row or recording panel
        const SizedBox(height: 16),
        if (isRecording)
          // Recording panel
          _buildRecordingPanel(context)
        else
          // Normal buttons row
          _buildEditButtonsRow(context),
      ],
    );
  }

  /// Build the recording panel for appending text to the idea
  Widget _buildRecordingPanel(BuildContext context) {
    // Store a reference to the scaffold messenger for later use
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    return AdvancedAudioRecordingPanel(
      layout: AudioRecordingPanelLayout.horizontal,
      showTimer: false,
      onRecordingCompleted: (filePath, duration) async {
        Logger.debug(
          'Idea edit recording completed: $filePath, duration: ${duration.inSeconds}s',
        );

        try {
          // Use the LLM service to transcribe the audio
          final llmService = ref.read(llmServiceProvider);
          final transcriptionResult = await llmService.transcribeAudio(
            filePath,
            useCase: TranscriptionUseCase.newIdea,
          );

          if (transcriptionResult.isSuccess &&
              transcriptionResult.idea != null) {
            // Get the transcribed text
            final transcribedText = transcriptionResult.idea!;
            Logger.debug('Transcription successful: $transcribedText');

            // If the widget is still mounted, directly append the text
            if (mounted) {
              _appendTranscribedText(transcribedText);
            } else {
              // If the widget is not mounted, store the transcription result in the provider
              // This will trigger the UI update when the widget is rebuilt
              Logger.debug(
                'Widget not mounted, storing transcription in provider',
              );
              ref
                  .read(ideaEditTranscriptionProvider.notifier)
                  .state = IdeaEditTranscription(
                ideaId: widget.idea.id,
                transcribedText: transcribedText,
              );
            }
          } else {
            // Handle transcription failure
            Logger.error(
              'Transcription failed: ${transcriptionResult.errorMessage}',
            );
            if (mounted) {
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Transcription failed'),
                  duration: Duration(seconds: 2),
                ),
              );
            }
          }
        } catch (e) {
          Logger.error('Error processing recording', e);
          if (mounted) {
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('Error processing recording: $e'),
                duration: const Duration(seconds: 2),
              ),
            );
          }
        } finally {
          // Exit recording mode
          if (mounted) {
            ref.read(ideaEditRecordingProvider.notifier).state = null;
          }
        }
      },
      onRecordingCancelled: () {
        // Exit recording mode
        ref.read(ideaEditRecordingProvider.notifier).state = null;
      },
      onRecordingFailed: (errorMessage) {
        Logger.error('Recording failed: $errorMessage');
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text('Recording failed: $errorMessage'),
              duration: const Duration(seconds: 2),
            ),
          );
          // Exit recording mode
          ref.read(ideaEditRecordingProvider.notifier).state = null;
        }
      },
      onPermissionDenied: () {
        Logger.error('Microphone permission denied');
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Microphone permission required'),
              duration: Duration(seconds: 2),
            ),
          );
          // Exit recording mode
          ref.read(ideaEditRecordingProvider.notifier).state = null;
        }
      },
    );
  }

  /// Build the buttons row for the edit mode
  Widget _buildEditButtonsRow(BuildContext context) {
    // Store a reference to the scaffold messenger for later use
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // Mic button (left-aligned) - changed to IconButton without outline
        IconButton(
          icon: Icon(Icons.mic, color: VojiTheme.colorsOf(context).textPrimary),
          onPressed: () async {
            // Check if microphone permission is granted
            final recordingService = ref.read(audioRecordingServiceProvider);
            final hasPermission = await recordingService.checkPermission();

            if (hasPermission) {
              // Enter recording mode
              if (mounted) {
                ref.read(ideaEditRecordingProvider.notifier).state =
                    widget.idea.id;
              }
              return;
            }

            // Request permission if not granted
            final status = await recordingService.requestPermission();
            if (status.isGranted) {
              // Enter recording mode
              if (mounted) {
                ref.read(ideaEditRecordingProvider.notifier).state =
                    widget.idea.id;
              }
            } else {
              // Show permission denied message
              if (mounted) {
                scaffoldMessenger.showSnackBar(
                  const SnackBar(
                    content: Text('Microphone permission required'),
                    duration: Duration(seconds: 2),
                  ),
                );
              }
            }
          },
          padding: EdgeInsets.zero,
          constraints: const BoxConstraints(),
        ),

        // Right-aligned buttons
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Cancel button - changed to TextButton without icon or outline
            TextButton(
              onPressed: () {
                // Reset text controller to original content before exiting edit mode
                _resetToOriginalContent();
                // Exit edit mode without saving
                ref.read(ideaEditProvider.notifier).state = null;
              },
              child: Text(
                'Cancel',
                style: TextStyle(
                  color: VojiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ),
            const SizedBox(width: 16), // Space between buttons
            // Save button - changed to TextButton without icon or outline
            TextButton(
              onPressed: () {
                _saveChanges(context, _textEditingController.text);
              },
              child: Text(
                'Save',
                style: TextStyle(
                  color: VojiTheme.colorsOf(context).textPrimary,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  /// Reset the text controller to the original idea content
  void _resetToOriginalContent() {
    Logger.debug(
      'Resetting text controller to original content for idea ${widget.idea.id}',
    );

    // Reset the text to the original idea content
    _currentText = widget.idea.content;
    _textEditingController.text = _currentText;

    // Reset flags
    _hasAppendedText = false;
    _justAppendedText = false;

    Logger.debug(
      'Text controller reset to original content: "${_currentText.substring(0, _currentText.length.clamp(0, 20))}${_currentText.length > 20 ? "..." : ""}"',
    );
  }

  /// Save the edited idea content
  void _saveChanges(BuildContext context, String newContent) {
    Logger.debug('_saveChanges - Idea ID: ${widget.idea.id}');
    Logger.debug(
      '_saveChanges - Original content: "${widget.idea.content.substring(0, widget.idea.content.length.clamp(0, 20))}${widget.idea.content.length > 20 ? "..." : ""}"',
    );
    Logger.debug(
      '_saveChanges - New content: "${newContent.substring(0, newContent.length.clamp(0, 20))}${newContent.length > 20 ? "..." : ""}"',
    );

    final trimmedNewContent = newContent.trim();

    // Check if the content exceeds the maximum length
    final maxWords = _maxIdeaWords ?? 1000; // Fallback to free tier limit if not initialized
    if (trimmedNewContent.length > maxWords) {
      Logger.debug(
        'Content exceeds maximum length of $maxWords characters',
      );

      // Show error dialog and unfocus the text field (keyboard will dismiss)
      _showContentTooLongDialog(context, maxWords);

      // Don't exit edit mode, allowing the user to continue editing by tapping the input again
      return;
    }

    // Only update if the content has changed and is not empty
    if (trimmedNewContent.isNotEmpty &&
        trimmedNewContent != widget.idea.content) {
      Logger.debug('Content has changed, updating idea in Firestore');

      // Create updated idea
      final updatedIdea = widget.idea.copyWith(
        content: trimmedNewContent,
        updatedAt: DateTime.now(),
      );

      // Store the BuildContext for later use
      final scaffoldMessenger = ScaffoldMessenger.of(context);

      Logger.debug('Using Firestore provider for idea update');

      // Use the ideabook ID passed to the widget
      final ideabookId = widget.ideabookId;

      // Update the idea using Firestore provider
      final updateFuture = ref
          .read(firestoreIdeasNotifierProvider(ideabookId).notifier)
          .updateIdea(updatedIdea);

      updateFuture
          .then((success) {
            Logger.debug('Idea update result: $success');
            // Only show notification if the update was successful and the content actually changed
            // The FirestoreService will return success=true even if no update was needed
            // (when content is the same), but in that case we don't want to show a notification
            if (success && trimmedNewContent != widget.idea.content) {
              // Show notification as a SnackBar
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Idea updated'),
                  duration: Duration(seconds: 1),
                ),
              );
            } else if (!success) {
              // Show error notification only if the update failed
              scaffoldMessenger.showSnackBar(
                const SnackBar(
                  content: Text('Failed to update idea'),
                  duration: Duration(seconds: 2),
                  backgroundColor: Colors.red,
                ),
              );
            }
          })
          .catchError((error) {
            Logger.error('Error updating idea', error);
            scaffoldMessenger.showSnackBar(
              SnackBar(
                content: Text('Error updating idea: $error'),
                duration: const Duration(seconds: 2),
                backgroundColor: Colors.red,
              ),
            );
          });
    } else {
      Logger.debug('Content has not changed or is empty, not updating');
    }

    // Exit edit mode regardless of whether content was changed
    ref.read(ideaEditProvider.notifier).state = null;

    // Reset flags when exiting edit mode
    _hasAppendedText = false;
    _justAppendedText = false;
  }

  /// Show a dialog when the idea content is too long
  Future<void> _showContentTooLongDialog(BuildContext context, int maxWords) async {
    // Set flag to prevent auto-focusing the text field
    setState(() {
      _shouldFocusTextField = false;
    });

    // Unfocus the text field to dismiss the keyboard
    _focusNode.unfocus();

    await showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Content Too Long! 📏',
            style: VojiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            "This idea is over the $maxWords character limit. Shorten it a touch, and it's good to save! 👍",
            style: VojiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: VojiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(),
              child: Text(
                'OK',
                style: VojiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
          ],
        );
      },
    );
  }

  /// Show a confirmation dialog before deleting an idea
  Future<void> _showDeleteConfirmationDialog(BuildContext context) async {
    // Store the BuildContext for later use
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final vojiThemeColors = VojiTheme.colorsOf(context);

    // Show confirmation dialog
    final bool? confirmDelete = await showDialog<bool>(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: Text(
            'Delete Idea',
            style: VojiTheme.textStylesOf(dialogContext).bodyLarge,
          ),
          content: Text(
            'Are you sure you want to delete this idea?',
            style: VojiTheme.textStylesOf(dialogContext).bodyMedium,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.zero,
            side: BorderSide(
              color: VojiTheme.colorsOf(dialogContext).border,
              width: 1,
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(false),
              child: Text(
                'Cancel',
                style: VojiTheme.textStylesOf(dialogContext).buttonText,
              ),
            ),
            TextButton(
              onPressed: () => Navigator.of(dialogContext).pop(true),
              child: Text(
                'Delete',
                style: VojiTheme.textStylesOf(dialogContext).buttonText
                    .copyWith(color: VojiTheme.colorsOf(dialogContext).error),
              ),
            ),
          ],
        );
      },
    );

    // If user confirmed deletion
    if (confirmDelete == true) {
      // Delete the idea
      try {
        Logger.debug('Using Firestore provider for idea deletion');

        // Use the ideabook ID passed to the widget
        final ideabookId = widget.ideabookId;

        // Delete the idea using Firestore provider
        final deleteFuture = ref
            .read(firestoreIdeasNotifierProvider(ideabookId).notifier)
            .deleteIdea(widget.idea.id);

        final success = await deleteFuture;

        if (success) {
          // Show notification
          scaffoldMessenger.showSnackBar(
            const SnackBar(
              content: Text('Idea deleted'),
              duration: Duration(seconds: 1),
            ),
          );
        }
      } catch (e) {
        Logger.error('Error deleting idea', e);
        // Show error message if deletion fails
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('Failed to delete idea: ${e.toString()}'),
            duration: const Duration(seconds: 2),
            backgroundColor: vojiThemeColors.error,
          ),
        );
      }
    }

    // Close the context menu regardless of deletion confirmation
    ref.read(swipedIdeaIdProvider.notifier).state = null;
  }
}
